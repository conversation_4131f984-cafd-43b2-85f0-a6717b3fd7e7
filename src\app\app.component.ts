import { Component, OnInit } from '@angular/core';
import { PlatformService } from './shared/utils/platform.service';
import { RouterOutlet } from '@angular/router';


@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  constructor(private platformService: PlatformService) {}

  ngOnInit() {
    // Example of platform-specific code
    if (this.platformService.isBrowser()) {
      console.log('Running in browser');
    }
  }
  title = 'ffa-cms';
}
