 <mat-card class="cash-requests-card">
      <mat-card-header>
        <mat-card-title> <h1 class="heading">My Cash Requests </h1></mat-card-title>
        <mat-card-subtitle><p class="subtitle">Track your current and past cash requests</p></mat-card-subtitle>
      </mat-card-header>


        <mat-card-content>
        <mat-tab-group class="requests-tabs">
          <mat-tab label="Pending Requests">
            <div class="tab-content">
             
            </div>
          </mat-tab>



            <mat-tab label="Approved Requests">
            <div class="tab-content">
            </div>
          </mat-tab>


           <mat-tab label="Issued Requests">
            <div class="tab-content">
            </div>
          </mat-tab>

            <mat-tab label="Rejected Requests">
            <div class="tab-content">
            </div>
          </mat-tab>


           <mat-tab label="Completed Requests">
            <div class="tab-content">
             
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card-content>
    </mat-card>



   <!-- second <header></header> -->
    <!-- Static Table without loader -->
<mat-table [dataSource]="dataSource" class="mat-elevation-z8">

  <ng-container matColumnDef="id">
    <mat-header-cell *matHeaderCellDef> Request ID </mat-header-cell>
    <mat-cell *matCellDef="let request"> REQ-{{ request.id }} </mat-cell>
  </ng-container>

  <ng-container matColumnDef="createdAt">
    <mat-header-cell *matHeaderCellDef> Date Requested </mat-header-cell>
    <mat-cell *matCellDef="let request"> {{ request.createdAt | date:'mediumDate' }} </mat-cell>
  </ng-container>

  <ng-container matColumnDef="cashTotal">
    <mat-header-cell *matHeaderCellDef> Amount </mat-header-cell>
    <mat-cell *matCellDef="let request"> R{{ request.cashTotal }} </mat-cell>
  </ng-container>

  <ng-container matColumnDef="requestStatus">
    <mat-header-cell *matHeaderCellDef> Status </mat-header-cell>
    <mat-cell *matCellDef="let request"> {{ request.requestStatus }} </mat-cell>
  </ng-container>

  <ng-container matColumnDef="issuedTo">
    <mat-header-cell *matHeaderCellDef> Issued To </mat-header-cell>
    <mat-cell *matCellDef="let request"> {{ request.issuedTo }} </mat-cell>
    
<ng-container matColumnDef="expectedReturn">
  <mat-header-cell *matHeaderCellDef> Expected Return </mat-header-cell>
  <mat-cell *matCellDef="let request">
    <!-- Example: Placeholder or actual value if your data includes it -->
    {{ request.expectedReturn }}
  </mat-cell>
</ng-container>

    <ng-container matColumnDef="action">
  <th mat-header-cell *matHeaderCellDef>Action</th>
  <td mat-cell *matCellDef="let request">
    <button mat-icon-button>
      <mat-icon>visibility</mat-icon>
    </button>
  </td>
  </ng-container>

  <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
  <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>


