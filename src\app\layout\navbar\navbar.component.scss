mat-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--backgound-white);
    margin: 0px;
    padding: 0px;
    border: 1px solid;
    border-color: var(--backgound-white);
    box-shadow: 0 3px 5px #888888; // rgba(0, 0, 0, 0.1);, not sure which color to use

}

.left {
    height: 100%;
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: var(--absa-red);
    padding: 2px 0px;
    padding-right: 5%;
}

.left #image {
    height: 60px;
    display: flex;
    align-items: center;
}

.left #image img {
    height: 100%;
    object-fit: contain;
    display: block;
}

.left #header {
    font-weight: 500;
    color: white;
    margin-left: 4px;
    font-size: 20px;
    display: flex;
    align-items: center;
    height: 100%;
    padding-left: 10px;
}


.center {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.right {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: auto;
}

.right .profile {
    display: flex;
    align-items: center;
    padding-right: 10px;
}

.right .username {
    margin-left: 8px;
}

.right .menu-items {
    padding-right: 10px;
}