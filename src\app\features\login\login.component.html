<div class="login-container">
    <h2>Login</h2>
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <div class="form-group">
            <mat-form-field appearance="outline">
                <mat-label>Email</mat-label>
                <input 
                    matInput 
                    formControlName="email"
                    [ngClass]="{ 'is-invalid': loginForm.get('email')?.touched && loginForm.get('email')?.invalid }"
                >
            </mat-form-field>
            <mat-form-field>
                <mat-label>Password</mat-label>
                <input 
                    matInput 
                    formControlName="password"
                    [ngClass]="{ 'is-invalid': loginForm.get('password')?.touched && loginForm.get('password')?.invalid }"
                >
            </mat-form-field>
        </div>
        <div class="login-buttom">
            <button matButton [disabled]="loading || loginForm.invalid" >
                {{ loading ? 'Logging in...' : 'Login' }}
            </button>
        </div>
    </form>
</div>
