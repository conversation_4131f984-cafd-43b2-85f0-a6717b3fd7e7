<div class="dashboard-container">
  <div class="dashboard-header">
    <h1>Cash Issuer Dashboard</h1>
    <p>Cash Management - Issue and Approve Cash Requests</p>
  </div>

  <div class="dashboard-content">
    <!-- Welcome Section -->
    <app-landingboard></app-landingboard>

    <!-- Quick Actions -->
    <mat-card class="quick-actions-card">
      <mat-card-header>
        <mat-card-title>Quick Actions</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="action-buttons">
          <button mat-raised-button color="primary" (click)="onManageRequests()">
            <mat-icon>assignment</mat-icon>
            Manage Requests
          </button>
          <button mat-raised-button color="accent" (click)="onInventoryManagement()">
            <mat-icon>inventory</mat-icon>
            Inventory Management
          </button>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Request Stats -->
    <app-request-stats></app-request-stats>
  </div>
</div>
