.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--absa-red);
  padding: 20px;
  font-family: '<PERSON><PERSON>', sans-serif;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 48px 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.logo-container {
  margin-bottom: 32px;

  .absa-logo {
    width: 80px;
    height: 80px;
    object-fit: contain;
  }
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 40px 0;
  letter-spacing: -0.5px;
}

.login-form {
  text-align: left;
}

.form-section {
  width: 100%;
}

.form-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin: 0 0 24px 0;
  text-align: left;
}

.input-group {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #555;
  margin-bottom: 8px;
}

.login-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
  background: white;
  transition: all 0.2s ease;
  box-sizing: border-box;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: var(--absa-red);
    box-shadow: 0 0 0 3px rgba(145, 29, 47, 0.1);
  }

  &.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
  }
}

.login-button {
  width: 100%;
  padding: 14px 24px;
  background: var(--absa-red);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;

  &:hover:not(:disabled) {
    background: var(--absa-red-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(145, 29, 47, 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.error-message {
  color: #dc3545;
  font-size: 14px;
  margin-top: 12px;
  padding: 8px 12px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  text-align: center;
}

.forgot-password {
  text-align: center;
  margin-top: 24px;

  .forgot-link {
    color: var(--absa-red);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .login-page {
    padding: 16px;
  }

  .login-card {
    padding: 32px 24px;
    max-width: 100%;
  }

  .welcome-title {
    font-size: 20px;
  }

  .logo-container .absa-logo {
    width: 60px;
    height: 60px;
  }
}
